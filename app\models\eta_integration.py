"""
نماذج البيانات للتكامل مع مصلحة الضرائب المصرية
Egyptian Tax Authority (ETA) Integration Models
"""

import uuid
import json
from datetime import datetime, timezone
from app import db

class ETATaxTransaction(db.Model):
    """معاملات الضرائب مع مصلحة الضرائب المصرية"""
    __tablename__ = 'eta_tax_transactions'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    invoice_id = db.Column(db.String(36), db.<PERSON><PERSON><PERSON>('invoices.id', ondelete='CASCADE'))
    receipt_id = db.Column(db.String(36), db.<PERSON>Key('receipts.id', ondelete='CASCADE'))
    transaction_type = db.Column(db.String(20), nullable=False)  # 'invoice' or 'receipt'
    
    # ETA Response Fields
    eta_uuid = db.Column(db.String(100))  # UUID من مصلحة الضرائب
    eta_internal_id = db.Column(db.String(100))  # Internal ID من مصلحة الضرائب
    submission_uuid = db.Column(db.String(100))  # UUID الإرسال
    long_id = db.Column(db.String(200))  # Long ID من مصلحة الضرائب
    hash_key = db.Column(db.String(500))  # Hash Key للتوقيع الرقمي
    
    # Status and Response
    sent_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    response_status = db.Column(db.String(50), default='pending')
    response_message = db.Column(db.Text)
    response_data = db.Column(db.JSON)  # البيانات الكاملة للاستجابة
    
    # Retry Logic
    retry_count = db.Column(db.Integer, default=0)
    last_retry_at = db.Column(db.DateTime)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # Relationships
    invoice = db.relationship('Invoice', backref='eta_tax_transactions')
    receipt = db.relationship('Receipt', backref='eta_tax_transactions')
    
    def __init__(self, transaction_type, invoice_id=None, receipt_id=None):
        self.transaction_type = transaction_type
        self.invoice_id = invoice_id
        self.receipt_id = receipt_id
    
    def to_dict(self):
        return {
            'id': self.id,
            'transaction_type': self.transaction_type,
            'invoice_id': self.invoice_id,
            'receipt_id': self.receipt_id,
            'eta_uuid': self.eta_uuid,
            'eta_internal_id': self.eta_internal_id,
            'submission_uuid': self.submission_uuid,
            'long_id': self.long_id,
            'response_status': self.response_status,
            'response_message': self.response_message,
            'retry_count': self.retry_count,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class ETASettings(db.Model):
    """إعدادات التكامل مع مصلحة الضرائب"""
    __tablename__ = 'eta_settings'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    setting_key = db.Column(db.String(100), unique=True, nullable=False)
    setting_value = db.Column(db.Text)
    is_encrypted = db.Column(db.Boolean, default=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    def __init__(self, setting_key, setting_value, description=None, is_encrypted=False):
        self.setting_key = setting_key
        self.setting_value = setting_value
        self.description = description
        self.is_encrypted = is_encrypted
    
    @staticmethod
    def get_setting(key, default=None):
        """الحصول على قيمة إعداد"""
        setting = ETASettings.query.filter_by(setting_key=key).first()
        return setting.setting_value if setting else default
    
    @staticmethod
    def set_setting(key, value, description=None, is_encrypted=False):
        """تعيين قيمة إعداد"""
        setting = ETASettings.query.filter_by(setting_key=key).first()
        if setting:
            setting.setting_value = value
            setting.updated_at = datetime.now(timezone.utc)
        else:
            setting = ETASettings(key, value, description, is_encrypted)
            db.session.add(setting)
        db.session.commit()
        return setting

class ETATaxCodes(db.Model):
    """أكواد الضرائب المصرية (EGS Codes)"""
    __tablename__ = 'eta_tax_codes'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    code_type = db.Column(db.String(50), nullable=False)  # 'EGS', 'GS1', etc.
    item_code = db.Column(db.String(100), nullable=False)
    code_name_ar = db.Column(db.String(200))
    code_name_en = db.Column(db.String(200))
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    parent_code = db.Column(db.String(100))
    is_active = db.Column(db.Boolean, default=True)
    eta_response_data = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    __table_args__ = (db.UniqueConstraint('code_type', 'item_code'),)
    
    def __init__(self, code_type, item_code, code_name_ar=None, code_name_en=None):
        self.code_type = code_type
        self.item_code = item_code
        self.code_name_ar = code_name_ar
        self.code_name_en = code_name_en
    
    def to_dict(self):
        return {
            'id': self.id,
            'code_type': self.code_type,
            'item_code': self.item_code,
            'code_name_ar': self.code_name_ar,
            'code_name_en': self.code_name_en,
            'description_ar': self.description_ar,
            'description_en': self.description_en,
            'parent_code': self.parent_code,
            'is_active': self.is_active
        }

class ETAErrorLog(db.Model):
    """سجل الأخطاء والمحاولات"""
    __tablename__ = 'eta_error_log'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    transaction_id = db.Column(db.String(36), db.ForeignKey('eta_tax_transactions.id'))
    error_type = db.Column(db.String(50), nullable=False)
    error_code = db.Column(db.String(20))
    error_message = db.Column(db.Text)
    error_details = db.Column(db.JSON)
    occurred_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationship
    transaction = db.relationship('ETATaxTransaction', backref='error_logs')
    
    def __init__(self, transaction_id, error_type, error_message, error_code=None, error_details=None):
        self.transaction_id = transaction_id
        self.error_type = error_type
        self.error_message = error_message
        self.error_code = error_code
        self.error_details = error_details
    
    def to_dict(self):
        return {
            'id': self.id,
            'transaction_id': self.transaction_id,
            'error_type': self.error_type,
            'error_code': self.error_code,
            'error_message': self.error_message,
            'error_details': self.error_details,
            'occurred_at': self.occurred_at.isoformat() if self.occurred_at else None
        }
